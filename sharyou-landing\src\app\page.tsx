'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import AIBuilderSection from '@/components/AIBuilderSection';
import FeaturesSection from '@/components/FeaturesSection';
import TestimonialsSection from '@/components/TestimonialsSection';
import Footer from '@/components/Footer';

export default function Home() {
  const [language, setLanguage] = useState<'fr' | 'ar'>('fr');

  return (
    <div className={`min-h-screen bg-white ${language === 'ar' ? 'rtl' : 'ltr'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <Header language={language} setLanguage={setLanguage} />
      <main className="relative">
        <HeroSection language={language} />
        <div className="section-gap">
          <AIBuilderSection language={language} />
        </div>
        <div className="section-gap">
          <FeaturesSection language={language} />
        </div>
        <div className="section-gap">
          <TestimonialsSection language={language} />
        </div>
      </main>
      <Footer language={language} />
    </div>
  );
}
