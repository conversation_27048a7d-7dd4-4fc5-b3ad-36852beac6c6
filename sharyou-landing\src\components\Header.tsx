'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { ChevronDownIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

interface HeaderProps {
  language: 'fr' | 'ar';
  setLanguage: (lang: 'fr' | 'ar') => void;
}

const translations = {
  fr: {
    features: 'Fonctionnalités',
    pricing: 'Tarifs',
    resources: 'Ressources',
    support: 'Support',
    login: 'Connexion',
    signup: 'Commencer gratuitement'
  },
  ar: {
    features: 'الميزات',
    pricing: 'الأسعار',
    resources: 'الموارد',
    support: 'الدعم',
    login: 'تسجيل الدخول',
    signup: 'ابدأ مجاناً'
  }
};

export default function Header({ language, setLanguage }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const t = translations[language];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMenuOpen]);

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-lg'
          : 'bg-white/80 backdrop-blur-sm border-b border-gray-100'
      }`}
    >
      <div className="container-max-width container-padding">
        <div className="flex justify-between items-center h-18 md:h-20 lg:h-24">
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex items-center"
          >
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-md">
                <span className="text-white font-bold text-lg md:text-xl">S</span>
              </div>
              <span className="text-xl md:text-2xl font-bold text-gray-900">Sharyou</span>
            </div>
          </motion.div>

          <nav className="hidden md:flex items-center space-x-8 lg:space-x-12">
            <a href="#features" className="nav-link">
              {t.features}
            </a>
            <a href="#pricing" className="nav-link">
              {t.pricing}
            </a>
            <a href="#resources" className="nav-link">
              {t.resources}
            </a>
            <a href="#support" className="nav-link">
              {t.support}
            </a>
          </nav>

          <div className="hidden md:flex items-center space-x-6 lg:space-x-8">
            <div className="relative">
              <button
                onClick={() => setIsLangDropdownOpen(!isLangDropdownOpen)}
                className="flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors rounded-lg hover:bg-gray-50 min-h-[44px]"
              >
                <span className="text-sm font-medium">
                  {language === 'fr' ? 'FR' : 'العربية'}
                </span>
                <ChevronDownIcon className="w-4 h-4" />
              </button>
              
              {isLangDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full mt-2 right-0 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[120px]"
                >
                  <button
                    onClick={() => {
                      setLanguage('fr');
                      setIsLangDropdownOpen(false);
                    }}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    Français
                  </button>
                  <button
                    onClick={() => {
                      setLanguage('ar');
                      setIsLangDropdownOpen(false);
                    }}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    العربية
                  </button>
                </motion.div>
              )}
            </div>

            <a href="#login" className="nav-link px-4 py-2 min-h-[44px] flex items-center">
              {t.login}
            </a>

            <motion.a
              href="#signup"
              whileHover={{ scale: 1.02, y: -1 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
              className="btn-primary"
            >
              {t.signup}
            </motion.a>
          </div>

          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-blue-600 transition-colors"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="md:hidden border-t border-gray-100 bg-white shadow-lg"
            >
            <div className="p-6 space-y-6">
              <div className="space-y-4">
                <a href="#features" className="block nav-link text-lg">
                  {t.features}
                </a>
                <a href="#pricing" className="block nav-link text-lg">
                  {t.pricing}
                </a>
                <a href="#resources" className="block nav-link text-lg">
                  {t.resources}
                </a>
                <a href="#support" className="block nav-link text-lg">
                  {t.support}
                </a>
              </div>

              <div className="pt-6 border-t border-gray-100 space-y-4">
                <div className="flex justify-center space-x-4">
                  <button
                    onClick={() => setLanguage('fr')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors min-h-[44px] ${
                      language === 'fr'
                        ? 'bg-blue-100 text-blue-600'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    Français
                  </button>
                  <button
                    onClick={() => setLanguage('ar')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors min-h-[44px] ${
                      language === 'ar'
                        ? 'bg-blue-100 text-blue-600'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    العربية
                  </button>
                </div>

                <div className="flex flex-col space-y-3">
                  <a href="#login" className="text-center nav-link text-lg py-3">
                    {t.login}
                  </a>
                  <a href="#signup" className="btn-primary text-center">
                    {t.signup}
                  </a>
                </div>
              </div>
            </div>
          </motion.div>
        )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
}
