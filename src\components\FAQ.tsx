import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  QuestionMarkCircleIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

const FAQ: React.FC = () => {
  const { t } = useTranslation();
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: t('faq_cost_question'),
      answer: t('faq_cost_answer')
    },
    {
      question: t('faq_ai_tools_included_question'),
      answer: t('faq_ai_tools_included_answer')
    },
    {
      question: t('faq_payment_methods_question'),
      answer: t('faq_payment_methods_answer')
    },
    {
      question: t('faq_store_creation_time_question'),
      answer: t('faq_store_creation_time_answer')
    },
    {
      question: t('faq_support_languages_question'),
      answer: t('faq_support_languages_answer')
    },
    {
      question: t('faq_international_selling_question'),
      answer: t('faq_international_selling_answer')
    },
    {
      question: t('faq_delivery_question'),
      answer: t('faq_delivery_answer')
    },
    {
      question: t('faq_data_security_question'),
      answer: t('faq_data_security_answer')
    }
  ];

  return (
    <section id={t('header_faq_id')} className="py-20 bg-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-20"></div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <QuestionMarkCircleIcon className="w-4 h-4" />
            {t('faq_section_badge')}
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('faq_title')}
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            {t('faq_subtitle')}
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4 mb-16">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300">
              <button
                className="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors group"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <span className="font-semibold text-gray-900 text-lg pr-4 group-hover:text-blue-600 transition-colors">
                  {faq.question}
                </span>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
                  openIndex === index
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-400 group-hover:bg-blue-100 group-hover:text-blue-600'
                }`}>
                  {openIndex === index ? (
                    <ChevronUpIcon className="w-5 h-5" />
                  ) : (
                    <ChevronDownIcon className="w-5 h-5" />
                  )}
                </div>
              </button>

              {openIndex === index && (
                <div className="px-8 pb-6 border-t border-gray-100 bg-gray-50">
                  <div className="pt-6">
                    <p className="text-gray-700 leading-relaxed text-lg">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Contact CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white">
            <ChatBubbleLeftRightIcon className="w-12 h-12 mx-auto mb-4 text-blue-200" />
            <h3 className="text-2xl font-bold mb-4">
              {t('faq_contact_prompt')}
            </h3>
            <p className="text-blue-100 mb-6 text-lg">
              {t('faq_contact_description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                {t('faq_contact_button')}
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                {t('faq_schedule_call_button')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;