import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  UserPlusIcon,
  BuildingStorefrontIcon,
  RocketLaunchIcon,
  ClockIcon,
  CheckIcon,
  ArrowRightIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const Process: React.FC = () => {
  const { t } = useTranslation();
  const steps = [
    {
      icon: UserPlusIcon,
      number: "01",
      title: "Inscription & Choix du Template",
      description: "Créez votre compte gratuitement et choisissez parmi plus de 100 templates professionnels adaptés à votre secteur d'activité.",
      time: "2 minutes",
      details: [
        "Création de compte gratuite",
        "Galerie de templates premium",
        "Aperçu des designs",
        "Sélection en un clic"
      ],
      color: "from-blue-600 to-indigo-600"
    },
    {
      icon: BuildingStorefrontIcon,
      number: "02",
      title: "Ajout des Produits",
      description: "Importez vos produits facilement avec notre IA qui génère automatiquement les descriptions et optimise vos images.",
      time: "10 minutes",
      details: [
        "Upload de photos produits",
        "Descriptions générées par IA",
        "Configuration prix & stock",
        "Organisation par catégories"
      ],
      color: "from-purple-600 to-pink-600"
    },
    {
      icon: RocketLaunchIcon,
      number: "03",
      title: "Personnalisation & Lancement",
      description: "Personnalisez votre boutique, configurez les paiements et la livraison, puis lancez votre business en ligne.",
      time: "5 minutes",
      details: [
        "Personnalisation design & marque",
        "Configuration moyens de paiement",
        "Paramétrage zones de livraison",
        "Lancement de votre boutique"
      ],
      color: "from-green-600 to-emerald-600"
    }
  ];

  return (
    <section id={t('header_process_id')} className="py-20 bg-gradient-to-br from-indigo-600 via-blue-600 to-purple-700 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-50"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
            <StarIcon className="w-4 h-4" />
            Processus Simplifié
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
            Créez votre boutique en 3 étapes simples
          </h2>
          <p className="text-xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
            Notre processus révolutionnaire vous permet de lancer votre boutique en ligne en moins de 20 minutes.
            Pas besoin de compétences techniques, tout est automatisé et guidé.
          </p>
        </div>

        {/* Steps Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {steps.map((step, index) => (
            <div key={index} className="relative group">
              {/* Connector Arrow - Hidden on mobile */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 -right-4 z-10">
                  <ArrowRightIcon className="w-8 h-8 text-white/60" />
                </div>
              )}

              {/* Step Card */}
              <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-300 group-hover:scale-105">
                {/* Step Icon & Number */}
                <div className="relative mb-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${step.color} rounded-2xl flex items-center justify-center mx-auto shadow-xl group-hover:scale-110 transition-transform duration-300`}>
                    <step.icon className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-gray-900 font-bold text-sm shadow-lg">
                    {step.number}
                  </div>
                </div>

                {/* Time Badge */}
                <div className="flex items-center justify-center gap-2 bg-white/20 rounded-full px-4 py-2 mb-6 w-fit mx-auto">
                  <ClockIcon className="w-4 h-4 text-white" />
                  <span className="text-white text-sm font-medium">{step.time}</span>
                </div>

                {/* Step Content */}
                <h3 className="text-xl font-bold text-white mb-4 text-center">
                  {step.title}
                </h3>

                <p className="text-blue-100 leading-relaxed text-center mb-6">
                  {step.description}
                </p>

                {/* Step Details */}
                <div className="space-y-3">
                  {step.details.map((detail, detailIndex) => (
                    <div key={detailIndex} className="flex items-center gap-3">
                      <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
                        <CheckIcon className="w-3 h-3 text-green-900" />
                      </div>
                      <span className="text-blue-100 text-sm">{detail}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA Section */}
        <div className="text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 max-w-2xl mx-auto">
            <div className="flex items-center justify-center gap-2 text-green-400 mb-4">
              <ClockIcon className="w-5 h-5" />
              <span className="font-semibold">Temps total : 17 minutes</span>
            </div>

            <h3 className="text-2xl font-bold text-white mb-4">
              Prêt à commencer ?
            </h3>

            <p className="text-blue-100 mb-6">
              Rejoignez plus de 50 000 commerçants qui ont choisi Sharyou pour créer leur business en ligne
            </p>

            <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group">
              Créer ma boutique maintenant
              <ArrowRightIcon className="w-5 h-5 inline-block ml-2 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Process;