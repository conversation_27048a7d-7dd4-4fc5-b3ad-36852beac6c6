import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  ShieldCheckIcon,
  LockClosedIcon,
  ClockIcon,
  CheckBadgeIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
  PhoneIcon,
  DocumentCheckIcon
} from '@heroicons/react/24/outline';

const TrustSection: React.FC = () => {
  const { t } = useTranslation();

  const trustBadges = [
    {
      icon: ShieldCheckIcon,
      title: t('trust_ssl_title'),
      description: t('trust_ssl_description'),
      color: 'from-green-600 to-emerald-600'
    },
    {
      icon: LockClosedIcon,
      title: t('trust_data_title'),
      description: t('trust_data_description'),
      color: 'from-blue-600 to-indigo-600'
    },
    {
      icon: ClockIcon,
      title: t('trust_uptime_title'),
      description: t('trust_uptime_description'),
      color: 'from-purple-600 to-pink-600'
    },
    {
      icon: PhoneIcon,
      title: t('trust_support_title'),
      description: t('trust_support_description'),
      color: 'from-orange-600 to-red-600'
    }
  ];

  const certifications = [
    {
      name: 'ISO 27001',
      description: t('trust_iso_description'),
      icon: DocumentCheckIcon
    },
    {
      name: 'PCI DSS',
      description: t('trust_pci_description'),
      icon: CurrencyDollarIcon
    },
    {
      name: 'GDPR',
      description: t('trust_gdpr_description'),
      icon: GlobeAltIcon
    },
    {
      name: 'SOC 2',
      description: t('trust_soc_description'),
      icon: CheckBadgeIcon
    }
  ];

  const guarantees = [
    {
      title: t('trust_guarantee1_title'),
      description: t('trust_guarantee1_description'),
      value: '99.9%'
    },
    {
      title: t('trust_guarantee2_title'),
      description: t('trust_guarantee2_description'),
      value: '24/7'
    },
    {
      title: t('trust_guarantee3_title'),
      description: t('trust_guarantee3_description'),
      value: '30 Days'
    },
    {
      title: t('trust_guarantee4_title'),
      description: t('trust_guarantee4_description'),
      value: '256-bit'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-30"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <ShieldCheckIcon className="w-4 h-4" />
            {t('trust_section_badge')}
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('trust_section_title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('trust_section_subtitle')}
          </p>
        </div>

        {/* Trust Badges */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {trustBadges.map((badge, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center hover:shadow-xl transition-all duration-300">
              <div className={`w-16 h-16 bg-gradient-to-r ${badge.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                <badge.icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {badge.title}
              </h3>
              <p className="text-gray-600 text-sm">
                {badge.description}
              </p>
            </div>
          ))}
        </div>

        {/* Guarantees */}
        <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 mb-16">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            {t('trust_guarantees_title')}
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {guarantees.map((guarantee, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {guarantee.value}
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {guarantee.title}
                </h4>
                <p className="text-gray-600 text-sm">
                  {guarantee.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Certifications */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">
            {t('trust_certifications_title')}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {certifications.map((cert, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300">
                <cert.icon className="w-8 h-8 text-gray-600 mx-auto mb-3" />
                <div className="font-bold text-gray-900 mb-2">{cert.name}</div>
                <p className="text-gray-600 text-xs">{cert.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Trust Statement */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              {t('trust_bottom_title')}
            </h3>
            <p className="text-blue-100 mb-6 text-lg max-w-3xl mx-auto">
              {t('trust_bottom_description')}
            </p>
            <div className="flex flex-wrap items-center justify-center gap-6 text-blue-100">
              <div className="flex items-center gap-2">
                <ShieldCheckIcon className="w-5 h-5" />
                <span className="text-sm">Bank-level Security</span>
              </div>
              <div className="flex items-center gap-2">
                <ClockIcon className="w-5 h-5" />
                <span className="text-sm">99.9% Uptime SLA</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckBadgeIcon className="w-5 h-5" />
                <span className="text-sm">SOC 2 Compliant</span>
              </div>
              <div className="flex items-center gap-2">
                <PhoneIcon className="w-5 h-5" />
                <span className="text-sm">24/7 Expert Support</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustSection;
