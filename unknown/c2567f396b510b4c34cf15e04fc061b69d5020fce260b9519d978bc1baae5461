import React, { useState, useEffect } from 'react';
import { Bars3Icon, XMarkIcon, ShoppingBagIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';

import { useTranslation } from 'react-i18next';
import GlobeLanguageSelector from './GlobeLanguageSelector';

const smoothScrollTo = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    const headerHeight = 80; // Account for fixed header height
    const elementPosition = element.offsetTop - headerHeight;
    window.scrollTo({
      top: elementPosition,
      behavior: 'smooth'
    });
  }
};

const Header: React.FC = () => {
  const { t } = useTranslation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) { // Adjust this value as needed
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? 'bg-white shadow-md py-3' : 'bg-transparent backdrop-filter backdrop-blur-lg bg-opacity-30 py-4'}`}>
      <div className="max-w-6xl mx-auto pl-0 pr-6 sm:pr-8 lg:pr-12 xl:pr-16">
        <div className="flex justify-between items-center h-10">
          {/* Logo */}
          <div className="flex items-center gap-x-3">
            <a onClick={() => smoothScrollTo('hero')} className="flex items-center gap-x-3 cursor-pointer">
              <div className={`w-9 h-9 rounded-xl flex items-center justify-center shadow-lg transition-all duration-300 ${scrolled ? 'bg-gradient-to-r from-blue-600 to-indigo-600' : 'bg-white'}`}>
                <ShoppingBagIcon className={`w-5 h-5 transition-all duration-300 ${scrolled ? 'text-white' : 'text-blue-600'}`} />
              </div>
              <div className="flex flex-col">
                <span className={`text-2xl font-bold tracking-tight transition-all duration-300 ${scrolled ? 'bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent' : 'text-white'}`}>{t('sharyou')}</span>
                <span className={`text-xs font-medium tracking-wide transition-all duration-300 ${scrolled ? 'text-blue-600' : 'text-white/80'}`}>Plateforme e-commerce IA</span>
              </div>
            </a>
          </div>

          <nav className="hidden md:flex items-center flex-grow justify-center gap-x-8">
            <a onClick={() => smoothScrollTo(t('header_features_id'))} className={`font-medium tracking-wide transition-colors cursor-pointer ${scrolled ? 'text-gray-600 hover:text-blue-600' : 'text-white hover:text-gray-200'}`}>
              {t('header_features')}
            </a>
            <a onClick={() => smoothScrollTo(t('header_process_id'))} className={`font-medium tracking-wide transition-colors cursor-pointer ${scrolled ? 'text-gray-600 hover:text-blue-600' : 'text-white hover:text-gray-200'}`}>
              {t('header_process')}
            </a>
            <a onClick={() => smoothScrollTo(t('header_ai_tools_id'))} className={`font-medium tracking-wide transition-colors cursor-pointer ${scrolled ? 'text-gray-600 hover:text-blue-600' : 'text-white hover:text-gray-200'}`}>
              {t('header_ai_tools')}
            </a>
            <a onClick={() => smoothScrollTo(t('header_testimonials_id'))} className={`font-medium tracking-wide transition-colors cursor-pointer ${scrolled ? 'text-gray-600 hover:text-blue-600' : 'text-white hover:text-gray-200'}`}>
              {t('header_testimonials')}
            </a>
            <a onClick={() => smoothScrollTo(t('header_faq_id'))} className={`font-medium tracking-wide transition-colors cursor-pointer ${scrolled ? 'text-gray-600 hover:text-blue-600' : 'text-white hover:text-gray-200'}`}>
              {t('header_faq')}
            </a>

          </nav>

          <div className="flex items-center gap-x-4">
            <Link to="/signin" className={`font-medium tracking-wide transition-colors cursor-pointer ${scrolled ? 'text-gray-600 hover:text-blue-600' : 'text-white hover:text-gray-200'}`}>{t('signIn')}</Link>
            <Link to="/signup" className={`font-medium tracking-wide transition-colors cursor-pointer ${scrolled ? 'text-gray-600 hover:text-blue-600' : 'text-white hover:text-gray-200'}`}>{t('signUp')}</Link>
            <Link to="/signup" className="hidden md:inline-block bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-lg font-semibold shadow-md hover:shadow-xl transition-all duration-300 ml-2">Créer ma boutique</Link>
            <button
              className={`md:hidden p-2 rounded-lg transition-colors ${scrolled ? 'hover:bg-gray-100' : 'hover:bg-gray-700'}`}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <XMarkIcon className={`w-6 h-6 ${scrolled ? 'text-gray-600' : 'text-white'}`} /> : <Bars3Icon className={`w-6 h-6 ${scrolled ? 'text-gray-600' : 'text-white'}`} />} 
            </button>
            <div className="ml-4 hidden md:block">
              <GlobeLanguageSelector />
            </div>
          </div>
        </div>
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-100 bg-white/95 shadow-lg transition-all duration-500 animate-fadeInDown">
            <nav className="flex flex-col gap-y-4">
              <a onClick={() => { smoothScrollTo(t('header_features_id')); setIsMenuOpen(false); }} className="text-gray-600 hover:text-blue-600 transition-colors font-medium tracking-wide text-center py-2">
                {t('header_features')}
              </a>
              <a onClick={() => { smoothScrollTo(t('header_process_id')); setIsMenuOpen(false); }} className="text-gray-600 hover:text-blue-600 transition-colors font-medium tracking-wide text-center py-2">
                {t('header_process')}
              </a>
              <a onClick={() => { smoothScrollTo(t('header_ai_tools_id')); setIsMenuOpen(false); }} className="text-gray-600 hover:text-blue-600 transition-colors font-medium tracking-wide text-center py-2">
                {t('header_ai_tools')}
              </a>
              <a onClick={() => { smoothScrollTo(t('header_testimonials_id')); setIsMenuOpen(false); }} className="text-gray-600 hover:text-blue-600 transition-colors font-medium tracking-wide text-center py-2">
                {t('header_testimonials')}
              </a>
              <a onClick={() => { smoothScrollTo(t('header_faq_id')); setIsMenuOpen(false); }} className="text-gray-600 hover:text-blue-600 transition-colors font-medium tracking-wide text-center py-2">
                {t('header_faq')}
              </a>

              <Link to="/signin" onClick={() => setIsMenuOpen(false)} className="text-gray-600 hover:text-blue-600 transition-colors font-medium tracking-wide text-center py-2">
                {t('signIn')}
              </Link>
              <Link to="/signup" onClick={() => setIsMenuOpen(false)} className="text-gray-600 hover:text-blue-600 transition-colors font-medium tracking-wide text-center py-2">
                {t('signUp')}
              </Link>
              <div className="mt-4 text-center">
                <GlobeLanguageSelector />
              </div>
            </nav>
          </div>
        )}
        </div>
    </header>
  );
};

export default Header;