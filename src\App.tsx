import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

import Header from './components/Header';
import Hero from './components/Hero';
import PlatformStats from './components/PlatformStats';
import Features from './components/Features';
import PlatformShowcase from './components/PlatformShowcase';
import Process from './components/Process';
import AITools from './components/AITools';
import Testimonials from './components/Testimonials';
import PricingSection from './components/PricingSection';
import TrustSection from './components/TrustSection';
import FAQ from './components/FAQ';
import Footer from './components/Footer';
import ChatWidget from './components/ChatWidget';


const Home = () => {
  const { t } = useTranslation();
  return (
    <div className="flex-1">
      <Hero />
      <PlatformStats />
      <Features />
      <PlatformShowcase />
      <Process />
      <AITools />
      <Testimonials />
      <PricingSection />
      <TrustSection />
      <FAQ />
      <Footer />
    </div>
  );
};

function App() {
  const { i18n } = useTranslation();

  useEffect(() => {
    document.documentElement.dir = i18n.dir();
  }, [i18n, i18n.language]);

  return (
    <Router>
      <div className="flex flex-col min-h-screen">
        <Header />
        <Routes>
          <Route path="/" element={<Home />} />

        </Routes>
        <ChatWidget />
      </div>
    </Router>
  );
}

export default App;