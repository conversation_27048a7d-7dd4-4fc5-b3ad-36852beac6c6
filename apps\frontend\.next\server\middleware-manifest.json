{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f640fbf4._.js", "server/edge/chunks/[root-of-the-server]__28b4ebf0._.js", "server/edge/chunks/apps_frontend_edge-wrapper_f10ce9b4.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hyrj43aMTUrQld2kjVnQWxZNxpkWYUXNJLZtFhIaxfM=", "__NEXT_PREVIEW_MODE_ID": "a8e73503c9411f8e91573a8c6910befe", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b8e09c9b06a0096068a151c3d4100dc93d1e02b21b62dda72b73ff95bb6189a2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "88bd98744b0e71bbe5f92be887ea80010d7f739fd5ddef1879edf0e96f343b4c"}}}, "sortedMiddleware": ["/"], "functions": {}}