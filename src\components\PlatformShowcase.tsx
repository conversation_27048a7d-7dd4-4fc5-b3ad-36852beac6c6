import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ComputerDesktopIcon,
  DevicePhoneMobileIcon,
  ChartBarIcon,
  CogIcon,
  ShoppingBagIcon,
  PaintBrushIcon,
  CheckIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

const PlatformShowcase: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(0);

  const showcaseItems = [
    {
      id: 'dashboard',
      icon: ChartBarIcon,
      features: [
        'platform_showcase.dashboard_feature1',
        'platform_showcase.dashboard_feature2',
        'platform_showcase.dashboard_feature3'
      ]
    },
    {
      id: 'store_builder',
      icon: PaintBrushIcon,
      features: [
        'platform_showcase.store_builder_feature1',
        'platform_showcase.store_builder_feature2',
        'platform_showcase.store_builder_feature3'
      ]
    },
    {
      id: 'mobile_app',
      icon: DevicePhoneMobileIcon,
      features: [
        'platform_showcase.mobile_app_feature1',
        'platform_showcase.mobile_app_feature2',
        'platform_showcase.mobile_app_feature3'
      ]
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-30"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-indigo-100 text-indigo-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <ComputerDesktopIcon className="w-4 h-4" />
            {t('platform_showcase.badge')}
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('platform_showcase.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('platform_showcase.subtitle')}
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {showcaseItems.map((item, index) => (
            <button
              key={index}
              onClick={() => setActiveTab(index)}
              className={`flex items-center gap-3 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === index
                  ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:text-gray-900 shadow-md hover:shadow-lg'
              }`}
            >
              <item.icon className="w-5 h-5" />
              {t(`platform_showcase.${item.id}_title`)}
            </button>
          ))}
        </div>

        {/* Active Tab Content */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content Side */}
          <div className="space-y-8">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                {t(`platform_showcase.${showcaseItems[activeTab].id}_title`)}
              </h3>
              <p className="text-xl text-gray-600 leading-relaxed">
                {t(`platform_showcase.${showcaseItems[activeTab].id}_description`)}
              </p>
            </div>

            {/* Features List */}
            <div className="space-y-4">
              {showcaseItems[activeTab].features.map((feature, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <CheckIcon className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-gray-700 leading-relaxed">{t(`platform_showcase.${showcaseItems[activeTab].id}_feature${index + 1}`)}</span>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                {t('platform_showcase.try_now_button')}
              </button>
              <button className="flex items-center justify-center gap-2 bg-white text-gray-700 px-8 py-4 rounded-xl font-semibold border border-gray-200 hover:shadow-lg transition-all duration-300">
                <PlayIcon className="w-5 h-5" />
                {t('platform_showcase.watch_demo_button')}
              </button>
            </div>
          </div>

          {/* Mockup Side */}
          <div className="relative">
            {/* Browser/Device Frame */}
            <div className="bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-200">
              {/* Browser Header */}
              <div className="bg-gray-100 px-4 py-3 flex items-center gap-2 border-b border-gray-200">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <div className="flex-1 bg-white rounded-md px-3 py-1 mx-4">
                  <span className="text-gray-500 text-sm">{t('platform_showcase.mockup_url')}</span>
                </div>
              </div>
              
              {/* Mockup Content */}
              <div className="aspect-video bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center relative overflow-hidden">
                {/* Placeholder for actual mockup */}
                <div className="text-center">
                  {React.createElement(showcaseItems[activeTab].icon, {
                    className: "w-16 h-16 text-blue-600 mx-auto mb-4"
                  })}
                  <h4 className="text-lg font-semibold text-gray-700 mb-2">
                    {t(`platform_showcase.${showcaseItems[activeTab].id}_title`)}
                  </h4>
                  <p className="text-gray-500 text-sm">
                    {t('platform_showcase.mockup_coming_soon')}
                  </p>
                </div>
                
                {/* Floating Elements for Visual Interest */}
                <div className="absolute top-4 left-4 bg-white rounded-lg p-3 shadow-lg">
                  <ChartBarIcon className="w-6 h-6 text-blue-600" />
                </div>
                <div className="absolute top-4 right-4 bg-white rounded-lg p-3 shadow-lg">
                  <ShoppingBagIcon className="w-6 h-6 text-green-600" />
                </div>
                <div className="absolute bottom-4 left-4 bg-white rounded-lg p-3 shadow-lg">
                  <CogIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>

            {/* Floating Stats */}
            <div className="absolute -top-4 -right-4 bg-white rounded-xl p-4 shadow-xl border border-gray-100">
              <div className="text-2xl font-bold text-gray-900">99.9%</div>
              <div className="text-sm text-gray-600">{t('platform_showcase.uptime_label')}</div>
            </div>
            
            <div className="absolute -bottom-4 -left-4 bg-white rounded-xl p-4 shadow-xl border border-gray-100">
              <div className="text-2xl font-bold text-gray-900">50K+</div>
              <div className="text-sm text-gray-600">{t('platform_showcase.users_label')}</div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {t('platform_showcase.bottom_cta_title')}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('platform_showcase.bottom_cta_description')}
            </p>
            <button className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              {t('platform_showcase.bottom_cta_button')}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PlatformShowcase;
