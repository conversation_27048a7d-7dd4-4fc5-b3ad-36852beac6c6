'use client';

import { motion } from 'framer-motion';
import { 
  CursorArrowRaysIcon,
  PaintBrushIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon,
  CreditCardIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface FeaturesSectionProps {
  language: 'fr' | 'ar';
}

const translations = {
  fr: {
    title: 'Tout ce dont vous avez besoin',
    subtitle: 'Une plateforme complète pour réussir votre e-commerce en Algérie',
    features: [
      {
        icon: CursorArrowRaysIcon,
        title: 'Interface Drag & Drop',
        description: 'Personnalisez votre boutique facilement avec notre éditeur visuel intuitif. Aucune compétence technique requise.'
      },
      {
        icon: PaintBrushIcon,
        title: 'Personnalisation Avancée',
        description: 'Des milliers de templates et options de personnalisation pour créer une boutique unique à votre image.'
      },
      {
        icon: DevicePhoneMobileIcon,
        title: 'Optimisation Mobile',
        description: 'Votre boutique s\'adapte parfaitement à tous les écrans. Plus de 70% de vos clients achètent sur mobile.'
      },
      {
        icon: GlobeAltIcon,
        title: 'Support Multilingue',
        description: 'Français, Arabe, Berbère - Vendez dans toutes les langues parlées en Algérie avec traduction automatique.'
      },
      {
        icon: CreditCardIcon,
        title: 'Paiements Locaux',
        description: 'CCP, Baridimob, Carte Edahabia - Tous les moyens de paiement algériens intégrés nativement.'
      },
      {
        icon: ChartBarIcon,
        title: 'Analytics Avancés',
        description: 'Suivez vos ventes, analysez vos clients et optimisez votre business avec des rapports détaillés.'
      }
    ]
  },
  ar: {
    title: 'كل ما تحتاجه',
    subtitle: 'منصة شاملة لنجاح تجارتك الإلكترونية في الجزائر',
    features: [
      {
        icon: CursorArrowRaysIcon,
        title: 'واجهة السحب والإفلات',
        description: 'خصص متجرك بسهولة مع محررنا المرئي البديهي. لا حاجة لمهارات تقنية.'
      },
      {
        icon: PaintBrushIcon,
        title: 'تخصيص متقدم',
        description: 'آلاف القوالب وخيارات التخصيص لإنشاء متجر فريد يعكس هويتك.'
      },
      {
        icon: DevicePhoneMobileIcon,
        title: 'تحسين للهاتف المحمول',
        description: 'متجرك يتكيف تماماً مع جميع الشاشات. أكثر من 70% من عملائك يشترون عبر الهاتف.'
      },
      {
        icon: GlobeAltIcon,
        title: 'دعم متعدد اللغات',
        description: 'الفرنسية، العربية، الأمازيغية - بع بجميع اللغات المحكية في الجزائر مع ترجمة تلقائية.'
      },
      {
        icon: CreditCardIcon,
        title: 'مدفوعات محلية',
        description: 'الحساب الجاري البريدي، بريدي موب، بطاقة الذهبية - جميع وسائل الدفع الجزائرية مدمجة أصلياً.'
      },
      {
        icon: ChartBarIcon,
        title: 'تحليلات متقدمة',
        description: 'تتبع مبيعاتك، حلل عملاءك وحسّن عملك مع تقارير مفصلة.'
      }
    ]
  }
};

export default function FeaturesSection({ language }: FeaturesSectionProps) {
  const t = translations[language];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const cardVariants = {
    hidden: { y: 50, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="features" className="section-padding bg-gray-50">
      <div className="container-max-width container-padding">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="text-center element-spacing-xl"
        >
          <motion.h2
            variants={itemVariants}
            className="heading-lg text-gray-900 element-spacing-lg"
          >
            {t.title}
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-body-lg text-gray-600 max-w-4xl mx-auto px-4 sm:px-0"
          >
            {t.subtitle}
          </motion.p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          className="grid sm:grid-cols-2 lg:grid-cols-3 grid-spacing-md"
        >
          {t.features.map((feature, index) => {
            const Icon = feature.icon;
            
            return (
              <motion.div
                key={index}
                variants={cardVariants}
                whileHover={{
                  y: -10,
                  transition: { duration: 0.3 }
                }}
                className="bg-white rounded-2xl p-8 md:p-10 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group h-full"
              >
                <div className="content-spacing-sm">
                  <div className="w-16 h-16 md:w-18 md:h-18 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center element-spacing-sm group-hover:scale-110 transition-transform duration-300 shadow-md">
                    <Icon className="w-8 h-8 md:w-9 md:h-9 text-white" />
                  </div>
                  <h3 className="heading-sm text-gray-900 element-spacing-sm">
                    {feature.title}
                  </h3>
                  <p className="text-body text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>

                <div className="pt-4 border-t border-gray-100">
                  <motion.div
                    initial={{ width: 0 }}
                    whileInView={{ width: '100%' }}
                    transition={{ duration: 1, delay: index * 0.1 }}
                    className="h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"
                  />
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
            
            <div className="relative z-10">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                {language === 'fr' ? 'Prêt à révolutionner votre e-commerce ?' : 'مستعد لثورة في تجارتك الإلكترونية؟'}
              </h3>
              <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                {language === 'fr' 
                  ? 'Rejoignez les milliers d\'entrepreneurs algériens qui ont choisi Sharyou pour développer leur business en ligne.'
                  : 'انضم إلى آلاف رجال الأعمال الجزائريين الذين اختاروا شاريو لتطوير أعمالهم عبر الإنترنت.'
                }
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white text-blue-600 font-semibold py-4 px-8 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  {language === 'fr' ? 'Commencer maintenant' : 'ابدأ الآن'}
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-white text-white font-semibold py-4 px-8 rounded-xl hover:bg-white hover:text-blue-600 transition-colors"
                >
                  {language === 'fr' ? 'Voir les tarifs' : 'عرض الأسعار'}
                </motion.button>
              </div>
            </div>

            <div className="absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full"></div>
            <div className="absolute -bottom-8 -left-8 w-24 h-24 bg-white/10 rounded-full"></div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
