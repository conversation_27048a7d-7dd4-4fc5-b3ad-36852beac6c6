import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CheckIcon,
  XMarkIcon,
  StarIcon,
  RocketLaunchIcon,
  BuildingOfficeIcon,
  SparklesIcon,
  ArrowRightIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

const PricingSection: React.FC = () => {
  const { t } = useTranslation();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const plans = [
    {
      name: t('pricing_starter_name'),
      description: t('pricing_starter_description'),
      price: billingCycle === 'monthly' ? '0' : '0',
      originalPrice: null,
      period: billingCycle === 'monthly' ? t('pricing_per_month') : t('pricing_per_year'),
      icon: RocketLaunchIcon,
      color: 'from-blue-600 to-indigo-600',
      popular: false,
      features: [
        t('pricing_starter_feature1'),
        t('pricing_starter_feature2'),
        t('pricing_starter_feature3'),
        t('pricing_starter_feature4'),
        t('pricing_starter_feature5')
      ],
      limitations: [
        t('pricing_starter_limit1'),
        t('pricing_starter_limit2')
      ],
      cta: t('pricing_starter_cta')
    },
    {
      name: t('pricing_professional_name'),
      description: t('pricing_professional_description'),
      price: billingCycle === 'monthly' ? '2,500' : '25,000',
      originalPrice: billingCycle === 'monthly' ? '3,000' : '36,000',
      period: billingCycle === 'monthly' ? t('pricing_per_month') : t('pricing_per_year'),
      icon: SparklesIcon,
      color: 'from-purple-600 to-pink-600',
      popular: true,
      features: [
        t('pricing_professional_feature1'),
        t('pricing_professional_feature2'),
        t('pricing_professional_feature3'),
        t('pricing_professional_feature4'),
        t('pricing_professional_feature5'),
        t('pricing_professional_feature6'),
        t('pricing_professional_feature7')
      ],
      limitations: [],
      cta: t('pricing_professional_cta')
    },
    {
      name: t('pricing_enterprise_name'),
      description: t('pricing_enterprise_description'),
      price: t('pricing_enterprise_price'),
      originalPrice: null,
      period: '',
      icon: BuildingOfficeIcon,
      color: 'from-gray-600 to-gray-800',
      popular: false,
      features: [
        t('pricing_enterprise_feature1'),
        t('pricing_enterprise_feature2'),
        t('pricing_enterprise_feature3'),
        t('pricing_enterprise_feature4'),
        t('pricing_enterprise_feature5'),
        t('pricing_enterprise_feature6')
      ],
      limitations: [],
      cta: t('pricing_enterprise_cta')
    }
  ];

  return (
    <section className="py-20 bg-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-20"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <CurrencyDollarIcon className="w-4 h-4" />
            {t('pricing_section_badge')}
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('pricing_section_title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('pricing_section_subtitle')}
          </p>
        </div>

        {/* Billing Toggle */}
        <div className="flex items-center justify-center mb-12">
          <div className="bg-gray-100 rounded-xl p-1 flex">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                billingCycle === 'monthly'
                  ? 'bg-white text-gray-900 shadow-md'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {t('pricing_monthly')}
            </button>
            <button
              onClick={() => setBillingCycle('yearly')}
              className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 relative ${
                billingCycle === 'yearly'
                  ? 'bg-white text-gray-900 shadow-md'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {t('pricing_yearly')}
              <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                {t('pricing_save_badge')}
              </span>
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`relative bg-white rounded-3xl border-2 transition-all duration-300 hover:shadow-2xl ${
                plan.popular
                  ? 'border-purple-200 shadow-xl scale-105'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-bold flex items-center gap-2">
                    <StarIcon className="w-4 h-4" />
                    {t('pricing_most_popular')}
                  </div>
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <div className={`w-16 h-16 bg-gradient-to-r ${plan.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                    <plan.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600">{plan.description}</p>
                </div>

                {/* Pricing */}
                <div className="text-center mb-8">
                  <div className="flex items-baseline justify-center gap-2">
                    {plan.originalPrice && (
                      <span className="text-lg text-gray-400 line-through">{plan.originalPrice} DA</span>
                    )}
                    <span className="text-4xl font-bold text-gray-900">
                      {plan.price === 'Custom' ? plan.price : `${plan.price} DA`}
                    </span>
                  </div>
                  {plan.period && (
                    <span className="text-gray-600">/{plan.period}</span>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start gap-3">
                      <CheckIcon className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                  {plan.limitations.map((limitation, limitIndex) => (
                    <div key={limitIndex} className="flex items-start gap-3">
                      <XMarkIcon className="w-5 h-5 text-gray-400 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-500">{limitation}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <button
                  className={`w-full py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:-translate-y-1 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:shadow-xl'
                      : 'bg-gray-900 text-white hover:bg-gray-800 hover:shadow-lg'
                  }`}
                >
                  {plan.cta}
                  <ArrowRightIcon className="w-5 h-5 inline-block ml-2" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold mb-4">
              {t('pricing_bottom_cta_title')}
            </h3>
            <p className="text-blue-100 mb-6 text-lg">
              {t('pricing_bottom_cta_subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                {t('pricing_bottom_cta_primary')}
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                {t('pricing_bottom_cta_secondary')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
