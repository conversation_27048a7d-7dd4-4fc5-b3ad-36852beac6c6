import React, { useState } from 'react';
import { ChatBubbleOvalLeftEllipsisIcon, XMarkIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';

const ChatWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<{ text: string; sender: 'user' | 'bot'; timestamp: string }[]>([
    { text: 'Bonjour ! Comment puis-je vous aider aujourd\'hui ? 😊', sender: 'bot', timestamp: 'Maintenant' }
  ]);

  const handleSendMessage = () => {
    if (message.trim() !== '') {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const newMessages = [...messages, { text: message, sender: 'user', timestamp: `${hours}:${minutes}` }];
      setMessages(newMessages);
      setMessage('');

      // Simulate bot response
      setTimeout(() => {
        setMessages(prevMessages => [
          ...prevMessages,
          { text: 'Merci pour votre message. Un agent vous répondra sous peu.', sender: 'bot', timestamp: 'Maintenant' }
        ]);
      }, 1000);
    }
  };

  return (
    <>
      {/* Chat Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 z-50"
      >
        {isOpen ? <XMarkIcon className="w-5 h-5 sm:w-6 sm:h-6" /> : <ChatBubbleOvalLeftEllipsisIcon className="w-5 h-5 sm:w-6 sm:h-6" />}
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 w-80 md:w-96 bg-white rounded-lg shadow-2xl z-50 max-h-[500px] flex flex-col">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-3 sm:p-4 rounded-t-lg">
            <h3 className="font-semibold text-sm sm:text-base">Support Sharyou</h3>
            <p className="text-xs sm:text-sm text-blue-100">Nous sommes en ligne maintenant</p>
          </div>

          {/* Messages */}
          <div className="flex-1 p-3 sm:p-4 overflow-y-auto bg-gray-50">
            <div className="space-y-3 sm:space-y-4">
              {messages.map((msg, index) => (
                <div key={index} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'} space-x-2 sm:space-x-3`}>
                  {msg.sender === 'bot' && (
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <ChatBubbleOvalLeftEllipsisIcon className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                    </div>
                  )}
                  <div className={`${msg.sender === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'} p-2 sm:p-3 rounded-lg shadow-sm max-w-xs`}>
                    <p className="text-xs sm:text-sm">{msg.text}</p>
                    <p className="text-xs text-gray-300 mt-1">{msg.sender === 'bot' ? 'Support' : 'Vous'} • {msg.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Input */}
          <div className="p-3 sm:p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Tapez votre message..."
                className="flex-1 px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleSendMessage();
                  }
                }}
              />
              <button
                onClick={handleSendMessage}
                className="bg-blue-600 text-white p-1.5 sm:p-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PaperAirplaneIcon className="w-3 h-3 sm:w-4 sm:h-4" />
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">
              Temps de réponse moyen : 30 secondes
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default ChatWidget;