<!doctype html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Sharyou - La plateforme e-commerce leader en Algérie. Créez votre boutique en ligne en 5 minutes avec nos outils IA exclusifs. Support 24/7, commission la plus basse du marché." />
    <meta name="keywords" content="e-commerce Algérie, boutique en ligne, vente en ligne Algérie, plateforme e-commerce, IA commerce, Sharyou" />
    <meta name="author" content="Sharyou" />
    
    <!-- Open Graph -->
    <meta property="og:title" content="Sharyou - Créez votre boutique en ligne en Algérie" />
    <meta property="og:description" content="La plateforme e-commerce leader en Algérie avec outils IA exclusifs" />
    <meta property="og:type" content="website" />
    
    <title>Sharyou - Plateforme E-commerce Leader en Algérie | Outils IA Inclus</title>
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <style>
      * {
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      html {
        scroll-behavior: smooth;
        overflow-x: hidden;
      }
      
      body {
        overflow-x: hidden;
      }
      
      /* Modern Loading Screen */
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: linear-gradient(135deg, #2563eb 0%, #4f46e5 50%, #7c3aed 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
      }
      
      .loading.fade-out {
        opacity: 0;
        visibility: hidden;
      }
      
      /* Logo Container */
      .loading-logo {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        animation: logoFloat 3s ease-in-out infinite;
      }
      
      .loading-logo-icon {
        width: 3rem;
        height: 3rem;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }
      
      .loading-logo-text {
        font-size: 2rem;
        font-weight: 800;
        color: white;
        letter-spacing: -0.025em;
      }
      
      /* Shopping bag icon */
      .shopping-bag-icon {
        width: 1.5rem;
        height: 1.5rem;
        stroke: white;
        stroke-width: 2;
        fill: none;
      }
      
      /* Loading Animation */
      .loading-animation {
        position: relative;
        width: 80px;
        height: 80px;
        margin-bottom: 2rem;
      }
      
      .loading-ring {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 3px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
      }
      
      .loading-ring:nth-child(2) {
        width: 60px;
        height: 60px;
        top: 10px;
        left: 10px;
        border-width: 2px;
        animation-delay: -0.4s;
        animation-duration: 1s;
      }
      
      .loading-ring:nth-child(3) {
        width: 40px;
        height: 40px;
        top: 20px;
        left: 20px;
        border-width: 2px;
        animation-delay: -0.8s;
        animation-duration: 0.8s;
      }
      
      /* Loading Text */
      .loading-text {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.125rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        animation: textPulse 2s ease-in-out infinite;
      }
      
      .loading-subtext {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
        font-weight: 400;
        text-align: center;
        max-width: 280px;
        line-height: 1.5;
      }
      
      /* Progress Bar */
      .loading-progress {
        width: 200px;
        height: 3px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        margin-top: 1.5rem;
        overflow: hidden;
      }
      
      .loading-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), white, rgba(255, 255, 255, 0.8));
        border-radius: 2px;
        animation: progressSlide 2s ease-in-out infinite;
      }
      
      /* Floating Elements */
      .floating-elements {
        position: absolute;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
      }
      
      .floating-element {
        position: absolute;
        width: 6px;
        height: 6px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        animation: float 6s linear infinite;
      }
      
      .floating-element:nth-child(1) { left: 10%; animation-delay: 0s; }
      .floating-element:nth-child(2) { left: 20%; animation-delay: 1s; }
      .floating-element:nth-child(3) { left: 30%; animation-delay: 2s; }
      .floating-element:nth-child(4) { left: 40%; animation-delay: 3s; }
      .floating-element:nth-child(5) { left: 50%; animation-delay: 4s; }
      .floating-element:nth-child(6) { left: 60%; animation-delay: 5s; }
      .floating-element:nth-child(7) { left: 70%; animation-delay: 0.5s; }
      .floating-element:nth-child(8) { left: 80%; animation-delay: 1.5s; }
      .floating-element:nth-child(9) { left: 90%; animation-delay: 2.5s; }
      
      /* Animations */
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes logoFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
      
      @keyframes textPulse {
        0%, 100% { opacity: 0.9; }
        50% { opacity: 0.6; }
      }
      
      @keyframes progressSlide {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(300%); }
      }
      
      @keyframes float {
        0% {
          transform: translateY(100vh) scale(0);
          opacity: 0;
        }
        10% {
          opacity: 1;
        }
        90% {
          opacity: 1;
        }
        100% {
          transform: translateY(-100px) scale(1);
          opacity: 0;
        }
      }
      
      /* Prevent horizontal scroll */
      .max-w-7xl {
        max-width: min(80rem, calc(100vw - 2rem));
      }
      
      /* Responsive text sizing */
      @media (max-width: 640px) {
        .text-6xl { font-size: 2.5rem; }
        .text-5xl { font-size: 2.25rem; }
        .text-4xl { font-size: 2rem; }
        
        .loading-logo-icon {
          width: 2.5rem;
          height: 2.5rem;
        }
        
        .loading-logo-text {
          font-size: 1.75rem;
        }
        
        .shopping-bag-icon {
          width: 1.25rem;
          height: 1.25rem;
        }
        
        .loading-animation {
          width: 60px;
          height: 60px;
        }
        
        .loading-text {
          font-size: 1rem;
        }
        
        .loading-subtext {
          font-size: 0.8rem;
          max-width: 240px;
        }
        
        .loading-progress {
          width: 160px;
        }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading" id="loadingScreen">
        <!-- Floating Background Elements -->
        <div class="floating-elements">
          <div class="floating-element"></div>
          <div class="floating-element"></div>
          <div class="floating-element"></div>
          <div class="floating-element"></div>
          <div class="floating-element"></div>
          <div class="floating-element"></div>
          <div class="floating-element"></div>
          <div class="floating-element"></div>
          <div class="floating-element"></div>
        </div>
        
        <!-- Logo -->
        <div class="loading-logo">
          <div class="loading-logo-icon">
            <svg class="shopping-bag-icon" viewBox="0 0 24 24">
              <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/>
              <line x1="3" y1="6" x2="21" y2="6"/>
              <path d="m16 10a4 4 0 0 1-8 0"/>
            </svg>
          </div>
          <span class="loading-logo-text">Sharyou</span>
        </div>
        
        <!-- Loading Animation -->
        <div class="loading-animation">
          <div class="loading-ring"></div>
          <div class="loading-ring"></div>
          <div class="loading-ring"></div>
        </div>
        
        <!-- Loading Text -->
        <div class="loading-text">Chargement de votre plateforme...</div>
        <div class="loading-subtext">
          Préparation de vos outils IA exclusifs et de votre tableau de bord personnalisé
        </div>
        
        <!-- Progress Bar -->
        <div class="loading-progress">
          <div class="loading-progress-bar"></div>
        </div>
      </div>
    </div>
    
    <script>
      // Hide loading screen when page is fully loaded
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loadingScreen');
          if (loadingScreen) {
            loadingScreen.classList.add('fade-out');
            setTimeout(function() {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1500); // Show loading for at least 1.5 seconds for better UX
      });
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>