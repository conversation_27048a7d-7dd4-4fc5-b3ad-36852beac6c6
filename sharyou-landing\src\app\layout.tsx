import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Sharyou - Créez votre boutique e-commerce en 30 secondes avec l'IA",
  description: "Sharyou révolutionne l'e-commerce en Algérie. Notre IA génère automatiquement votre boutique complète, optimisée et prête à vendre en 30 secondes.",
  keywords: "e-commerce, Algérie, IA, boutique en ligne, vente en ligne, Sharyou",
  authors: [{ name: "Sharyou Team" }],
  openGraph: {
    title: "Sharyou - Plateforme e-commerce algérienne propulsée par l'IA",
    description: "Créez votre boutique en ligne en 30 secondes avec notre IA révolutionnaire",
    type: "website",
    locale: "fr_DZ",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
