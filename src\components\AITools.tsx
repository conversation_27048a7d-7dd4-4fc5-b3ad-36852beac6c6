import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  SparklesIcon,
  ChartBarIcon,
  UserGroupIcon,
  LightBulbIcon,
  BoltIcon,
  ArrowTrendingUpIcon,
  CpuChipIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';

const AITools: React.FC = () => {
  const { t } = useTranslation();
  const tools = [
    {
      icon: CpuChipIcon,
      title: t('ai_tool_predictive_sales_title'),
      description: t('ai_tool_predictive_sales_description'),
      benefits: [
        t('ai_tool_predictive_sales_benefit1'),
        t('ai_tool_predictive_sales_benefit2'),
        t('ai_tool_predictive_sales_benefit3')
      ],
      color: 'from-blue-600 to-indigo-600',
      impact: t('ai_tool_predictive_sales_impact')
    },
    {
      icon: ChartBarIcon,
      title: t('ai_tool_dynamic_pricing_title'),
      description: t('ai_tool_dynamic_pricing_description'),
      benefits: [
        t('ai_tool_dynamic_pricing_benefit1'),
        t('ai_tool_dynamic_pricing_benefit2'),
        t('ai_tool_dynamic_pricing_benefit3')
      ],
      color: 'from-green-600 to-emerald-600',
      impact: t('ai_tool_dynamic_pricing_impact')
    },
    {
      icon: UserGroupIcon,
      title: t('ai_tool_smart_customer_targeting_title'),
      description: t('ai_tool_smart_customer_targeting_description'),
      benefits: [
        t('ai_tool_smart_customer_targeting_benefit1'),
        t('ai_tool_smart_customer_targeting_benefit2'),
        t('ai_tool_smart_customer_targeting_benefit3')
      ],
      color: 'from-purple-600 to-pink-600',
      impact: t('ai_tool_smart_customer_targeting_impact')
    }
  ];

  const stats = [
    { value: "94%", label: t('ai_stat_prediction_accuracy') },
    { value: "+35%", label: t('ai_stat_sales_increase') },
    { value: "24/7", label: t('ai_stat_continuous_optimization') },
    { value: "15min", label: t('ai_stat_quick_setup') }
  ];

  return (
    <section id={t('header_ai_tools_id')} className="py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-50"></div>
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-purple-500/20 backdrop-blur-sm border border-purple-300/30 rounded-full text-purple-300 text-sm font-medium mb-6">
            <SparklesIcon className="w-4 h-4 mr-2" />
            {t('ai_tools_tag')}
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('ai_tools_title')}
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            {t('ai_tools_subtitle')}
          </p>
        </div>

        {/* AI Tools Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {tools.map((tool, index) => (
            <div key={index} className="group bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-500 hover:scale-105">
              {/* Impact Badge */}
              <div className="flex items-center justify-between mb-6">
                <div className={`w-16 h-16 bg-gradient-to-r ${tool.color} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-xl`}>
                  <tool.icon className="w-8 h-8 text-white" />
                </div>
                <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm font-bold">
                  {tool.impact}
                </div>
              </div>

              <h3 className="text-xl font-bold text-white mb-4">
                {tool.title}
              </h3>

              <p className="text-gray-300 mb-6 leading-relaxed">
                {tool.description}
              </p>

              <div className="space-y-3">
                {tool.benefits.map((benefit, idx) => (
                  <div key={idx} className="flex items-start gap-3">
                    <BoltIcon className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-300 text-sm">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Stats */}
        <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20">
          <h3 className="text-2xl font-bold text-white text-center mb-8">
            {t('ai_stats_proven_results')}
          </h3>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">
                  {stat.value}
                </div>
                <p className="text-gray-300 text-sm">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('ai_cta_title')}
            </h3>
            <p className="text-gray-300 mb-8">
              {t('ai_cta_description')}
            </p>
            <button className="group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
              {t('ai_cta_button')}
              <RocketLaunchIcon className="w-5 h-5 inline-block ml-2 group-hover:translate-x-1 transition-transform" />
            </button>
            <p className="text-gray-400 text-sm mt-4">
              {t('ai_cta_trial_info')}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AITools;