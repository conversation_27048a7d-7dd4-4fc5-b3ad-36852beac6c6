@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Visual Design System */
@layer utilities {
  /* Background Grid Pattern */
  .bg-grid-slate-100 {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(148 163 184 / 0.05)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
  }

  .bg-grid-white {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(255 255 255 / 0.1)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
  }

  /* Enhanced Gradients */
  .gradient-primary {
    background: linear-gradient(135deg, #2563eb 0%, #4f46e5 50%, #7c3aed 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #059669 0%, #0891b2 50%, #0284c7 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, #dc2626 0%, #ea580c 50%, #d97706 100%);
  }

  /* Glass Morphism */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced Shadows */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
  }

  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }

  /* Smooth Animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 3s infinite;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Custom animations for language selector */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-from-top-2 {
  from {
    transform: translateY(-8px);
  }
  to {
    transform: translateY(0);
  }
}

.animate-in {
  animation-duration: 200ms;
  animation-fill-mode: both;
}

.fade-in {
  animation-name: fade-in;
}

.slide-in-from-top-2 {
  animation-name: slide-in-from-top-2;
}

html,
body {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, Opera */
}

/* Add this to your index.css or a dedicated CSS file */

.animated-text-outline {
  -webkit-text-stroke: 1px #000; /* Outline color and width */
  -webkit-text-fill-color: transparent; /* Make the inside of the text transparent */
  animation: stroke-animation 3s infinite alternate;
}

@keyframes stroke-animation {
  0% {
    -webkit-text-stroke-color: #000;
  }
  100% {
    -webkit-text-stroke-color: #4F46E5; /* Example: change outline color */
  }
}
